/**
 * 构建脚本 - 包含缓存修复
 * 这个脚本会在构建后验证缓存配置是否正确
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始构建项目...');

try {
  // 执行构建
  console.log('📦 执行 vite build...');
  execSync('npm run build', { stdio: 'inherit' });

  // 验证构建结果
  console.log('🔍 验证构建结果...');
  
  // 检查 dist 目录
  const distPath = path.resolve(__dirname, '../dist');
  if (!fs.existsSync(distPath)) {
    throw new Error('dist 目录不存在');
  }

  // 检查关键文件
  const criticalFiles = [
    'index.html',
    'version.json',
    'web.config',
    'config.js'
  ];

  criticalFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  警告: ${file} 文件不存在`);
    } else {
      console.log(`✅ ${file} 文件存在`);
    }
  });

  // 验证 index.html 中的缓存控制
  const indexHtmlPath = path.join(distPath, 'index.html');
  if (fs.existsSync(indexHtmlPath)) {
    const indexContent = fs.readFileSync(indexHtmlPath, 'utf-8');
    
    // 检查缓存控制meta标签
    if (indexContent.includes('no-cache') && indexContent.includes('no-store')) {
      console.log('✅ index.html 包含正确的缓存控制meta标签');
    } else {
      console.warn('⚠️  警告: index.html 缺少缓存控制meta标签');
    }

    // 检查动态favicon脚本
    if (indexContent.includes('favicon') && indexContent.includes('new Date().getTime()')) {
      console.log('✅ index.html 包含动态favicon脚本');
    } else {
      console.warn('⚠️  警告: index.html 缺少动态favicon脚本');
    }
  }

  // 验证 web.config
  const webConfigPath = path.join(distPath, 'web.config');
  if (fs.existsSync(webConfigPath)) {
    const webConfigContent = fs.readFileSync(webConfigPath, 'utf-8');
    
    if (webConfigContent.includes('Cache-Control') && webConfigContent.includes('no-cache')) {
      console.log('✅ web.config 包含正确的缓存控制配置');
    } else {
      console.warn('⚠️  警告: web.config 缺少缓存控制配置');
    }
  }

  // 验证版本文件
  const versionJsonPath = path.join(distPath, 'version.json');
  if (fs.existsSync(versionJsonPath)) {
    const versionContent = JSON.parse(fs.readFileSync(versionJsonPath, 'utf-8'));
    
    if (versionContent.version && versionContent.buildTime) {
      console.log(`✅ version.json 正确: 版本 ${versionContent.version}, 构建时间 ${versionContent.buildTime}`);
    } else {
      console.warn('⚠️  警告: version.json 格式不正确');
    }
  }

  // 检查静态资源是否有hash
  const assetsPath = path.join(distPath, 'assets');
  if (fs.existsSync(assetsPath)) {
    const jsFiles = fs.readdirSync(path.join(assetsPath, 'js')).filter(f => f.endsWith('.js'));
    const cssFiles = fs.readdirSync(path.join(assetsPath, 'css')).filter(f => f.endsWith('.css'));
    
    const hasHashedFiles = [...jsFiles, ...cssFiles].some(file => file.includes('-') && file.match(/[a-f0-9]{8}/));
    
    if (hasHashedFiles) {
      console.log('✅ 静态资源包含hash值，有助于缓存控制');
    } else {
      console.warn('⚠️  警告: 静态资源缺少hash值');
    }
  }

  console.log('\n🎉 构建完成！缓存配置验证通过。');
  console.log('\n📋 缓存修复总结:');
  console.log('   ✅ HTML文件添加了no-cache meta标签');
  console.log('   ✅ web.config配置了强制缓存控制');
  console.log('   ✅ 版本文件包含构建时间戳');
  console.log('   ✅ favicon使用动态版本号');
  console.log('   ✅ 静态资源使用hash命名');
  console.log('\n🚀 现在可以部署到服务器了！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
}
