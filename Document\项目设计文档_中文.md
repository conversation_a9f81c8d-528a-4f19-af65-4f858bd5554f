# 项目设计文档 - TicketDemo (中文版)

## 1. 项目概述

### 1.1 项目背景与目标
本项目 (TicketDemo) 是一个基于前后端分离架构的工单管理系统（Case Management / Ticketing System）。旨在提供一个高效、灵活的工单处理平台，用于跟踪、管理和解决客户或内部用户的请求和问题。

**目标:**
*   实现工单的创建、分配、处理、跟踪和关闭全生命周期管理。
*   提供用户、角色、权限管理功能。
*   支持工作流引擎，实现工单流转的自动化。
*   提供清晰的数据展示和报表功能。
*   构建稳定、可扩展、易于维护的系统。

### 1.2 目标用户和使用场景
*   **目标用户:**
    *   系统管理员：负责系统配置、用户权限管理。
    *   服务台/支持人员：接收、分配、处理工单。
    *   普通用户/客户：提交工单、查看工单状态。
    *   部门经理/主管：审批工单、查看团队工单处理情况。
*   **使用场景:**
    *   IT 服务管理 (ITSM)：处理 IT 相关的请求和故障。
    *   客户服务：管理客户咨询、投诉和请求。
    *   内部事务处理：如 HR 请求、行政审批等。

### 1.3 技术选型
*   **前端:**
    *   框架: Vue 3
    *   构建工具: Vite
    *   语言: TypeScript
    *   UI 库: Element Plus
    *   状态管理: Pinia
    *   路由: Vue Router
    *   HTTP 请求: Axios
    *   图表: ECharts
    *   富文本编辑器: WangEditor
    *   工作流设计器: LogicFlow
    *   表格: VxeTable
*   **后端:**
    *   框架: ASP.NET Core 6
    *   附加框架: Furion
    *   语言: C#
    *   API 方案: RESTful API
    *   数据库: SQL Server (推断，基于 Serilog 和 Elsa 的配置)
    *   ORM: SqlSugar
    *   工作流引擎: Elsa Workflow
    *   认证: JWT (JSON Web Tokens)
    *   日志: Serilog (支持输出到控制台、文件、SQL Server)
    *   依赖注入: Autofac
*   **数据库:** SQL Server (推断)
*   **部署:** Docker (支持)

## 2. 前后端架构设计

### 2.1 整体架构概述
系统采用经典的前后端分离架构。
*   **前端 (Ticket.VueV2):** 负责用户界面展示、用户交互逻辑、向后端请求数据。基于 Vue 3 生态构建的单页应用 (SPA)。
*   **后端 (YunLanCrm.Api):** 负责业务逻辑处理、数据持久化、提供 RESTful API 接口供前端调用。基于 ASP.NET Core 6 和 Furion 框架构建。
*   **交互方式:** 前后端通过 **RESTful API** 进行通信，使用 JSON 格式传输数据。认证机制采用 **JWT**。

### 2.2 前端架构
*   **框架:** Vue 3 (Composition API) + Vite + TypeScript。
*   **状态管理:** Pinia，用于管理全局状态，如用户信息、主题配置、路由列表等。
*   **路由:** Vue Router，实现页面导航和权限控制（可能结合后端返回的菜单/路由信息）。
*   **组件组织:**
    *   `src/components`: 存放可复用的业务无关或基础组件 (如图标选择器、文件上传、工作流组件等)。
    *   `src/views`: 存放页面级组件，对应各个路由。
    *   `src/layout`: 存放整体页面布局结构组件 (导航栏、侧边栏、主内容区等)。
*   **模块化:** 按功能或业务领域划分目录结构 (如 `src/api`, `src/views` 下按模块划分)。

### 2.3 后端架构
采用分层架构设计，主要包括：
*   **展现层 (Controllers):** `YunLanCrm.Api/Controllers` 目录。负责接收前端 HTTP 请求，调用服务层处理业务逻辑，并返回响应。遵循 RESTful 风格。
*   **服务层 (Services):** `YunLanCrm.Services` (推断，基于 `YunLanCrm.IServices` 接口定义) 和 `YunLanCrm.Extensions`。包含核心业务逻辑的实现。
*   **接口层 (Interfaces):** `YunLanCrm.IServices` 目录。定义服务层接口，实现依赖倒置。
*   **数据访问层 (Repository):** `YunLanCrm.Repository` (推断，基于 `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Repository` 和 SqlSugar 配置)。负责与数据库交互，提供数据持久化操作，使用 SqlSugar ORM。
*   **领域模型 (Entities/Models):** `YunLanCrm.Model` (推断，基于 `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Entity`)。定义数据实体。
*   **通用/扩展层 (Common/Extensions):** `YunLanCrm.Common`, `YunLanCrm.Extensions`。提供通用工具类、中间件、服务扩展、AOP、认证授权等支持。
*   **工作流层 (Workflow):** `YunLanCrm.Api/ElsaWorkflows`, `YunLanCrm.WorkFlow`。集成 Elsa Workflow 实现业务流程自动化。

## 3. 前端设计

### 3.1 技术栈
*   **核心框架:** Vue 3.x
*   **构建工具:** Vite
*   **编程语言:** TypeScript
*   **UI 框架:** Element Plus
*   **路由管理:** Vue Router 4.x
*   **状态管理:** Pinia
*   **HTTP 客户端:** Axios
*   **CSS 预处理器:** Sass/SCSS
*   **代码规范:** ESLint, Prettier
*   **其他库:** ECharts, WangEditor, LogicFlow, VxeTable, CountUp.js, SortableJS, Screenfull 等。

### 3.2 组件划分
*   **目录结构:**
    *   `src/components`: 全局/可复用组件。
    *   `src/views`: 页面视图组件，按业务模块组织。
    *   `src/layout`: 布局相关组件。
*   **复用组件:** 封装了如图标选择器 (`iconSelector`)、文件上传 (`scUpload`)、富文本编辑器 (`editor`)、工作流设计器 (`scWorkflow`)、表格 (`table`) 等常用组件。
*   **状态管理 (Pinia Stores):**
    *   `userInfo`: 存储用户信息和权限。
    *   `themeConfig`: 主题配置。
    *   `routesList`: 动态路由列表。
    *   `keepAliveNames`: 需要缓存的组件名。
    *   `tagsViewRoutes`: 标签页导航状态。

### 3.3 路由设计
*   **模式:** Hash 或 History 模式 (由 `vite.config.ts` 和 Vue Router 配置决定)。
*   **路由定义:** `src/router/route.ts` 定义静态路由，`src/router/backEnd.ts` (推断) 处理后端返回的动态路由。
*   **导航逻辑:**
    *   侧边栏菜单根据路由配置动态生成。
    *   使用 Vue Router 的导航守卫 (`beforeEach`) 实现路由权限控制和登录状态校验。
    *   支持标签页导航 (TagsView)。

### 3.4 接口调用
*   **封装:** 在 `src/api` 目录下按模块封装 API 请求函数。
    *   `src/api/BaseApi.ts` (推断) 可能包含 Axios 实例的创建和基础配置 (拦截器等)。
    *   `src/utils/request.ts` (推断，或在 BaseApi 中实现) 封装 Axios，统一处理请求头、Token 附加、错误处理、响应拦截等。
*   **错误处理:** 在 Axios 拦截器中统一处理 HTTP 状态码错误和业务逻辑错误码，并给出用户提示 (如 Element Plus 的 Message 或 Notification)。

### 3.5 用户交互 & UI 设计
*   **UI 框架:** Element Plus 提供基础组件库和样式。
*   **自定义样式:** `src/theme` 目录下定义全局样式、主题变量、覆盖 Element Plus 样式以及特定组件的样式。
*   **响应式布局:** 可能通过 CSS Media Queries 或 Element Plus 的栅格系统实现，以适应不同屏幕尺寸。
*   **交互反馈:** 使用 NProgress 实现页面加载进度条，Element Plus 的 Loading、Message、Notification 等组件提供操作反馈。

## 4. 后端设计

### 4.1 技术栈
*   **核心框架:** ASP.NET Core 6.0
*   **增强框架:** Furion
*   **编程语言:** C#
*   **Web 服务器:** Kestrel (默认)，支持 IIS 部署
*   **数据库交互:** SqlSugar ORM
*   **工作流:** Elsa Workflow
*   **日志:** Serilog
*   **认证:** JWT
*   **依赖注入:** Autofac

### 4.2 模块划分 (按目录结构推断)
*   **Controllers (`YunLanCrm.Api/Controllers`):** API 入口，处理 HTTP 请求，参数校验，调用 Service。按业务领域划分 Controller (如 `CmTicketsController`, `UserController`)。
*   **Services (`YunLanCrm.Services`, `YunLanCrm.Extensions`):** 实现 `YunLanCrm.IServices` 中定义的接口，处理核心业务逻辑。
*   **Repositories (`YunLanCrm.Repository`):** 实现数据访问逻辑，通过 SqlSugar 操作数据库。
*   **Entities (`YunLanCrm.Model`):** 数据库表对应的实体类。
*   **Common (`YunLanCrm.Common`):** 通用工具类、常量、帮助类 (如 `StringHelper`, `UploadHelper`)、自定义授权、中间件基类等。
*   **Extensions (`YunLanCrm.Extensions`):** 服务注册扩展方法 (如 `AddSqlsugarSetup`, `AddAuthentication_JWTSetup`)、中间件、AOP 实现、AutoMapper 配置等。
*   **Workflows (`YunLanCrm.Api/ElsaWorkflows`, `YunLanCrm.WorkFlow`):** Elsa 工作流定义、活动 (Activities) 实现。

### 4.3 数据模型设计
*   **ORM:** SqlSugar 用于对象关系映射。
*   **实体定义:** 在 `YunLanCrm.Model` 或 `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Entity` (推断) 中定义 C# 类对应数据库表。
*   **数据库表:** (需要进一步分析实体类或数据库脚本) 预计包含用户表、角色表、权限表、菜单表、工单主表、工单详情表、评论表、附件表、工作流实例表等。
*   **字段:** (需要进一步分析实体类) 包含主键、外键、业务字段、审计字段 (创建时间、创建人、修改时间、修改人等)。

### 4.4 API 设计
*   **规范:** RESTful API 风格。
*   **路由:** 使用特性路由 (`[Route]`, `[HttpGet]`, `[HttpPost]` 等)，并可能通过 `GlobalRoutePrefixFilter` 添加统一前缀 (如 `/api`)。
*   **数据格式:** 请求和响应主体使用 JSON 格式。通过 `AddNewtonsoftJson` 配置序列化选项 (如驼峰命名、日期格式)。
*   **端点说明:** (需要查看 Controller 代码和 XML 注释 `YunLanCrm.xml`) Controller 中的 Action 方法对应具体的 API 端点，XML 注释用于生成 Swagger 文档。
*   **认证机制:**
    *   通过 `[Authorize]` 特性保护需要认证的 API。
    *   JWT Bearer Token 认证：前端在请求头 `Authorization` 中携带 Token。
    *   后端通过 `AddAuthentication_JWTSetup` 配置 JWT 验证逻辑 (Issuer, Audience, Secret Key 等)。
    *   `/api/Token` (推断) 或类似端点用于用户登录并获取 Token。
*   **统一响应格式:** 使用 Furion 的 `AddInjectWithUnifyResult<RESTfulResultProvider>` 实现统一的 API 响应结构，包含成功/失败状态码、消息和数据。

### 4.5 异常处理与日志
*   **全局异常处理:** 通过 `GlobalExceptionsFilter` 捕获未处理的异常，记录日志并返回统一的错误响应。
*   **日志:**
    *   使用 Serilog 进行日志记录。
    *   配置了多个 Sink：Console, File (按日、按级别分割), SQL Server (`LogEvent` 表)。
    *   通过 `LogContext` 添加上下文信息。
    *   在 `Program.cs` 中配置日志级别、过滤规则和输出模板。
*   **错误码:** (需要检查代码实现) 可能定义了业务错误码用于区分不同的业务异常情况。

## 5. 数据库设计 (推断)

### 5.1 数据库架构
*   **类型:** Microsoft SQL Server。
*   **交互:** 通过 SqlSugar ORM 进行数据操作。
*   **连接字符串:** 配置在 `appsettings.json` 或其他配置文件中 (`Serilog:ConnectionString`, 以及 SqlSugar 的配置项)。

### 5.2 表结构 (推断，部分示例)
*   `Users`: 用户信息 (ID, 用户名, 密码哈希, 邮箱, 状态等)
*   `Roles`: 角色信息 (ID, 角色名)
*   `UserRoles`: 用户角色关系表
*   `Permissions`: 权限定义表 (ID, 权限标识, 名称)
*   `RolePermissions`: 角色权限关系表
*   `Menus`: 菜单信息表
*   `CmTickets`: 工单主表 (ID, 标题, 描述, 状态, 优先级, 创建人, 创建时间, 指派人等)
*   `CmTicketDetails` / `CmTicketContents`: 工单内容/处理记录表
*   `CmTicketComments`: 工单评论表
*   `CmTicketAttachments`: 工单附件表
*   `WorkflowInstances`: 工作流实例表 (Elsa)
*   `WorkflowExecutionLogRecords`: 工作流执行日志 (Elsa)
*   `LogEvent`: Serilog 日志记录表

### 5.3 数据存储与索引优化
*   (需要检查数据库脚本或 SqlSugar 配置) 可能对常用查询字段（如 ID、状态、创建时间、用户 ID 等）创建了索引以提高查询性能。

### 5.4 事务管理、数据一致性策略
*   (需要检查 Service 层代码) SqlSugar 支持事务操作。关键业务操作（如涉及多表更新）应包裹在事务中，确保原子性。
*   对于分布式事务或复杂一致性场景，可能需要引入 Saga 模式或分布式事务协调器 (代码中未明显体现)。

## 6. 安全性设计

### 6.1 前端安全
*   **XSS (跨站脚本攻击):**
    *   Vue 默认对模板插值进行 HTML 转义。
    *   对于 `v-html` 或富文本编辑器内容，需要进行严格的 XSS 过滤。
*   **CSRF (跨站请求伪造):**
    *   JWT 本身在一定程度上能防御 CSRF (如果 Token 存储在 LocalStorage/SessionStorage)。如果 Token 存储在 Cookie 中，后端需要实现 Anti-CSRF Token 机制。
*   **CORS (跨源资源共享):** 后端通过 `AddCorsSetup` 配置允许特定来源的前端访问。
*   **依赖库安全:** 定期更新依赖库，避免使用存在已知漏洞的版本。

### 6.2 后端安全
*   **身份认证:**
    *   基于 JWT 的 Token 认证。
    *   密码存储：应使用强哈希算法 (如 bcrypt, Argon2) 加盐存储。
*   **权限管理:**
    *   基于角色的访问控制 (RBAC)。
    *   通过 `[Authorize]` 特性结合角色或策略进行 API 权限控制。
    *   `YunLanCrm.Common/Authorizations` 和 `YunLanCrm.Extensions/Authorizations` 包含相关实现。
*   **数据加密:**
    *   传输层：应强制使用 HTTPS (代码中 `UseHttpsRedirection` 被注释，建议生产环境启用)。
    *   敏感数据：数据库中存储的敏感信息（如密码、密钥）应加密或哈希处理。
*   **输入验证:** 对所有来自前端的输入进行严格验证 (参数非空、格式、长度、类型等)，防止 SQL 注入、命令注入等攻击。ASP.NET Core 的模型绑定和验证机制可用于此目的。
*   **依赖注入安全:** 避免在服务实现中暴露过多内部细节。
*   **配置安全:** 敏感配置信息 (如数据库密码、JWT 密钥) 不应硬编码，应使用环境变量、Secrets Manager 或配置中心 (如 Apollo，代码中有相关配置但可能未启用)。

### 6.3 API 安全
*   **认证:** JWT Bearer Token。
*   **授权:** 基于角色的 API 访问控制。
*   **速率限制:** 通过 `AddIpPolicyRateLimitSetup` 配置 IP 速率限制，防止暴力破解和 DoS 攻击。
*   **参数校验:** 后端对 API 参数进行严格校验。
*   **防止信息泄露:** 全局异常处理机制避免直接暴露堆栈信息给客户端。

## 7. 部署与运维

### 7.1 前端构建与部署
*   **构建:** 使用 `npm run build` (或 `vite build`) 命令将 Vue 项目打包成静态文件 (HTML, CSS, JS)。
*   **部署:**
    *   将构建后的静态文件部署到 Web 服务器 (如 Nginx, IIS) 或对象存储 (如 Azure Blob Storage)。
    *   `public/web.config` 文件表明可能考虑 IIS 部署。
    *   CDN: 可选，用于加速静态资源访问。
    *   模式: 客户端渲染 (CSR) 是默认模式。

### 7.2 后端部署
*   **服务器架构:** 可以部署在 Windows Server (IIS) 或 Linux 服务器。
*   **容器化:** 项目提供了 `Dockerfile`，支持使用 Docker 进行容器化部署。
    *   `DockerBuild.bat`, `YunLanCrm.Publish.Docker.sh` 等脚本用于辅助 Docker 构建和发布。
*   **负载均衡:** (未在代码中直接体现) 生产环境通常需要配置负载均衡器 (如 Nginx, HAProxy, Azure Load Balancer) 将请求分发到多个后端实例。
*   **编排:** (未在代码中直接体现) 对于大规模部署，可使用 Kubernetes 等容器编排工具管理 Docker 容器。

### 7.3 CI/CD 流程
*   (需要检查项目仓库配置) 可能使用 GitHub Actions, Jenkins, GitLab CI 或 Azure DevOps 等工具实现自动化构建、测试和部署流程。
*   `YunLanCrm.Publish.Docker.Jenkins.sh` 文件暗示可能使用 Jenkins 进行 Docker 相关的 CI/CD。
*   `codecov.yml` 表明集成了 Codecov 进行代码覆盖率检查。

## 8. 测试与优化

### 8.1 前端测试
*   (代码库中未明显包含测试文件)
*   **单元测试:** 可使用 Vitest 或 Jest + Vue Testing Library 对组件、工具函数、Pinia stores 进行单元测试。
*   **端到端测试 (E2E):** 可使用 Cypress 或 Playwright 模拟用户操作，测试关键业务流程。

### 8.2 后端测试
*   (代码库中未明显包含测试项目)
*   **单元测试:** 可使用 xUnit 或 NUnit 对 Service 层、工具类进行单元测试，可能需要 Mock 依赖项 (如 Repository)。
*   **集成测试:** 可使用 `Microsoft.AspNetCore.Mvc.Testing` 对 API Controller 进行集成测试，测试请求处理、服务调用和数据库交互。
*   **API 测试:** 使用 Postman, Newman 或其他 API 测试工具对发布的 API 进行功能和回归测试。

### 8.3 性能优化
*   **前端:**
    *   **代码分割/懒加载:** Vite 和 Vue Router 默认支持基于路由的懒加载，按需加载页面组件。
    *   **静态资源优化:** `vite-plugin-compression` 用于压缩资源 (Gzip/Brotli)。`vite-plugin-cdn-import` 可能用于引入 CDN 资源。
    *   **图片优化:** 压缩图片大小。
    *   **虚拟滚动/列表优化:** 对于长列表，使用虚拟滚动技术 (VxeTable 可能支持)。
    *   **减少重绘重排:** 优化 DOM 操作。
    *   **缓存:** 合理利用浏览器缓存。Pinia 状态持久化。
*   **后端:**
    *   **数据库优化:**
        *   优化 SQL 查询 (SqlSugar 的性能)。
        *   添加合适的数据库索引。
    *   **缓存:**
        *   使用内存缓存 (`IMemoryCache`) 缓存热点数据或配置。
        *   考虑使用分布式缓存 (如 Redis，代码中有引入但被注释) 提高扩展性。
    *   **异步处理:** 对 I/O 密集型操作使用 `async/await` 异步编程。
    *   **响应压缩:** 启用了 Brotli 和 Gzip 压缩 (`AddResponseCompression`)。
    *   **代码优化:** 优化算法和数据结构。
    *   **性能分析:** 使用 MiniProfiler (`AddMiniProfilerSetup`) 定位性能瓶颈。
    *   **水平扩展:** 通过部署多个实例并使用负载均衡来提高吞吐量。
*   **API:**
    *   减少不必要的 API 调用。
    *   优化 API 响应数据大小。
