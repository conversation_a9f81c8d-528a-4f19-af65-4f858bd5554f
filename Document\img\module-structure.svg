<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600">
  <!-- 背景 -->
  <rect width="800" height="600" fill="#f5f7fa" rx="10" ry="10"/>
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#2c3e50">CaseX 模块结构</text>

  <!-- 中心节点 - CaseX系统 -->
  <circle cx="400" cy="300" r="70" fill="#3498db" stroke="#2980b9" stroke-width="3"/>
  <text x="400" y="295" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">CaseX</text>
  <text x="400" y="315" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white">管理系统</text>
  
  <!-- 模块1 - 认证模块 -->
  <circle cx="230" cy="150" r="60" fill="#e74c3c" stroke="#c0392b" stroke-width="2"/>
  <text x="230" y="145" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">认证模块</text>
  <text x="230" y="165" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">Token认证</text>
  
  <!-- 连接线1 -->
  <line x1="276" y1="196" x2="343" y2="251" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 模块2 - 工单管理 -->
  <circle cx="230" cy="450" r="60" fill="#f39c12" stroke="#d35400" stroke-width="2"/>
  <text x="230" y="445" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">工单管理</text>
  <text x="230" y="465" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">CRUD操作</text>
  
  <!-- 连接线2 -->
  <line x1="276" y1="404" x2="343" y2="349" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 模块3 - 工作流引擎 -->
  <circle cx="570" cy="150" r="60" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
  <text x="570" y="145" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">工作流引擎</text>
  <text x="570" y="165" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">流程自动化</text>
  
  <!-- 连接线3 -->
  <line x1="524" y1="196" x2="457" y2="251" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 模块4 - UI/主题系统 -->
  <circle cx="570" cy="450" r="60" fill="#1abc9c" stroke="#16a085" stroke-width="2"/>
  <text x="570" y="445" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">UI/主题</text>
  <text x="570" y="465" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="white">多主题支持</text>
  
  <!-- 连接线4 -->
  <line x1="524" y1="404" x2="457" y2="349" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 模块5 - 系统管理 -->
  <circle cx="400" cy="530" r="50" fill="#27ae60" stroke="#16a085" stroke-width="2"/>
  <text x="400" y="525" font-family="Arial, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="white">系统管理</text>
  <text x="400" y="545" font-family="Arial, sans-serif" font-size="11" text-anchor="middle" fill="white">用户与权限</text>
  
  <!-- 连接线5 -->
  <line x1="400" y1="480" x2="400" y2="370" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 模块6 - 国际化 -->
  <circle cx="400" cy="100" r="50" fill="#2ecc71" stroke="#27ae60" stroke-width="2"/>
  <text x="400" y="95" font-family="Arial, sans-serif" font-size="15" font-weight="bold" text-anchor="middle" fill="white">国际化</text>
  <text x="400" y="115" font-family="Arial, sans-serif" font-size="11" text-anchor="middle" fill="white">多语言支持</text>
  
  <!-- 连接线6 -->
  <line x1="400" y1="150" x2="400" y2="230" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 技术标签 -->
  <rect x="155" y="40" width="80" height="30" rx="15" ry="15" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="195" y="60" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">Vue 3</text>
  
  <rect x="245" y="40" width="100" height="30" rx="15" ry="15" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="295" y="60" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">TypeScript</text>
  
  <rect x="355" y="40" width="80" height="30" rx="15" ry="15" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="395" y="60" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">Vite</text>
  
  <rect x="445" y="40" width="120" height="30" rx="15" ry="15" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="505" y="60" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">Element Plus</text>
  
  <rect x="575" y="40" width="80" height="30" rx="15" ry="15" fill="#34495e" stroke="#2c3e50" stroke-width="1"/>
  <text x="615" y="60" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">Pinia</text>
</svg>
