// 错误处理函数测试文件
// 这个文件用于测试 handleError 函数的各种情况

import { handleError } from './index';

// 测试用例
console.log('=== 错误处理函数测试 ===');

// 测试1: 正常的错误消息
console.log('测试1: 正常的错误消息');
handleError({ resultMsg: 'This is an error message' });

// 测试2: 空的错误消息（不应该显示任何提示）
console.log('测试2: 空的错误消息');
handleError({ resultMsg: '' });

// 测试3: null 错误消息（不应该显示任何提示）
console.log('测试3: null 错误消息');
handleError({ resultMsg: null });

// 测试4: undefined 错误消息（不应该显示任何提示）
console.log('测试4: undefined 错误消息');
handleError({ resultMsg: undefined });

// 测试5: 只包含空白字符的错误消息（不应该显示任何提示）
console.log('测试5: 只包含空白字符的错误消息');
handleError({ resultMsg: '   ' });

// 测试6: 使用默认消息
console.log('测试6: 使用默认消息');
handleError({ resultMsg: '' }, 'Default error message');

// 测试7: 字符串错误
console.log('测试7: 字符串错误');
handleError('String error message');

// 测试8: 不同属性名的错误对象
console.log('测试8: 不同属性名的错误对象');
handleError({ ResultMsg: 'Error with capital R' });
handleError({ message: 'Error with message property' });
handleError({ msg: 'Error with msg property' });

// 测试9: 复杂的错误对象
console.log('测试9: 复杂的错误对象');
handleError({
  resultCode: 500,
  resultMsg: 'Server error',
  data: null,
  timestamp: new Date()
});

console.log('=== 测试完成 ===');
