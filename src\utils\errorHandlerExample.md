# 全局错误处理使用说明

## 概述

为了解决项目中 `rs.resultMsg` 可能为空导致显示空错误消息的问题，我们创建了一个全局错误处理函数 `handleError`。

## 功能特点

1. **智能消息提取**：自动从错误对象中提取消息（支持 `resultMsg`、`ResultMsg`、`message`、`msg` 等属性）
2. **空消息过滤**：如果错误消息为空或只包含空白字符，则不显示错误提示
3. **默认消息支持**：可以提供默认错误消息作为备选
4. **调试日志**：在开发环境下自动记录完整错误信息到控制台

## 使用方法

### 方法一：直接导入使用（推荐）

```javascript
import { handleError } from '/@/utils/index';

// 在 API 调用的 catch 中使用
someApi.getData()
  .then((rs) => {
    // 处理成功响应
  })
  .catch((rs) => {
    // 使用全局错误处理函数
    handleError(rs);
    // 或者提供默认消息
    // handleError(rs, 'Operation failed');
  });
```

### 方法二：使用全局属性（在 Vue 组件中）

```javascript
// 在 Vue 组件的 setup 或 methods 中
const { proxy } = getCurrentInstance();

someApi.getData()
  .then((rs) => {
    // 处理成功响应
  })
  .catch((rs) => {
    // 使用全局属性
    proxy.$handleError(rs);
    // 或者提供默认消息
    // proxy.$handleError(rs, 'Operation failed');
  });
```

## 替换现有代码

### 原来的代码：
```javascript
.catch((rs) => {
  ElMessage.error(rs.resultMsg);
})
```

### 替换为：
```javascript
.catch((rs) => {
  handleError(rs);
})
```

## 支持的错误格式

函数会自动处理以下格式的错误：

```javascript
// 字符串错误
handleError("Error message");

// 对象错误（会自动提取消息）
handleError({ resultMsg: "Error message" });
handleError({ ResultMsg: "Error message" });
handleError({ message: "Error message" });
handleError({ msg: "Error message" });

// 空消息（不会显示任何提示）
handleError({ resultMsg: "" });
handleError({ resultMsg: null });
handleError({ resultMsg: undefined });
handleError({ resultMsg: "   " }); // 只包含空白字符
```

## 配置

错误处理函数会根据 `appSettings.LOGGING_ENABLED` 配置决定是否在控制台输出调试信息。

## 注意事项

1. 如果错误消息为空，函数不会显示任何错误提示，避免了空白错误弹窗的问题
2. 函数会自动清理消息中的前后空白字符
3. 在开发环境下，完整的错误信息仍会记录到控制台，便于调试
4. 可以提供默认错误消息作为备选方案

## 迁移建议

建议逐步将项目中的错误处理代码迁移到使用 `handleError` 函数，特别是那些直接使用 `ElMessage.error(rs.resultMsg)` 的地方。
