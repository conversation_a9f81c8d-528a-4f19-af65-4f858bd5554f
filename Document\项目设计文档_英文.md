# Project Design Document - TicketDemo (English Version)

## 1. Project Overview

### 1.1 Project Background and Objectives
This project (TicketDemo) is a case management/ticketing system based on a frontend-backend separated architecture. It aims to provide an efficient and flexible platform for tracking, managing, and resolving requests and issues from customers or internal users.

**Objectives:**
*   Implement full lifecycle management for tickets: creation, assignment, processing, tracking, and closure.
*   Provide user, role, and permission management functionalities.
*   Support a workflow engine to automate ticket flow processes.
*   Offer clear data visualization and reporting features.
*   Build a stable, scalable, and maintainable system.

### 1.2 Target Users and Scenarios
*   **Target Users:**
    *   System Administrators: Responsible for system configuration and user permission management.
    *   Service Desk/Support Staff: Receive, assign, and process tickets.
    *   Regular Users/Customers: Submit tickets and view ticket status.
    *   Department Managers/Supervisors: Approve tickets and view team's ticket handling status.
*   **Usage Scenarios:**
    *   IT Service Management (ITSM): Handling IT-related requests and incidents.
    *   Customer Service: Managing customer inquiries, complaints, and requests.
    *   Internal Affairs Processing: Such as HR requests, administrative approvals, etc.

### 1.3 Technology Stack
*   **Frontend:**
    *   Framework: Vue 3
    *   Build Tool: Vite
    *   Language: TypeScript
    *   UI Library: Element Plus
    *   State Management: Pinia
    *   Routing: Vue Router
    *   HTTP Requests: Axios
    *   Charting: ECharts
    *   Rich Text Editor: WangEditor
    *   Workflow Designer: LogicFlow
    *   Table: VxeTable
*   **Backend:**
    *   Framework: ASP.NET Core 6
    *   Additional Framework: Furion
    *   Language: C#
    *   API Scheme: RESTful API
    *   Database: SQL Server (Inferred, based on Serilog and Elsa configurations)
    *   ORM: SqlSugar
    *   Workflow Engine: Elsa Workflow
    *   Authentication: JWT (JSON Web Tokens)
    *   Logging: Serilog (Supports output to console, file, SQL Server)
    *   Dependency Injection: Autofac
*   **Database:** SQL Server (Inferred)
*   **Deployment:** Docker (Supported)

## 2. Frontend & Backend Architecture

### 2.1 Overall Architecture Overview
The system adopts a classic frontend-backend separated architecture.
*   **Frontend (Ticket.VueV2):** Responsible for UI display, user interaction logic, and requesting data from the backend. It's a Single Page Application (SPA) built with the Vue 3 ecosystem.
*   **Backend (YunLanCrm.Api):** Responsible for business logic processing, data persistence, and providing RESTful API interfaces for the frontend. Built with ASP.NET Core 6 and the Furion framework.
*   **Interaction:** Frontend and backend communicate via **RESTful API** using JSON format for data transfer. Authentication is handled using **JWT**.

### 2.2 Frontend Architecture
*   **Framework:** Vue 3 (Composition API) + Vite + TypeScript.
*   **State Management:** Pinia, used for managing global state like user info, theme configuration, route lists, etc.
*   **Routing:** Vue Router, implementing page navigation and permission control (potentially combined with menu/route info from the backend).
*   **Component Organization:**
    *   `src/components`: Stores reusable, business-agnostic, or base components (e.g., icon selector, file upload, workflow component).
    *   `src/views`: Stores page-level components corresponding to routes.
    *   `src/layout`: Stores components for the overall page layout structure (navbar, sidebar, main content area).
*   **Modularity:** Directory structure organized by feature or business domain (e.g., `src/api`, `src/views` subdirectories by module).

### 2.3 Backend Architecture
Employs a layered architecture design, primarily including:
*   **Presentation Layer (Controllers):** `YunLanCrm.Api/Controllers` directory. Receives frontend HTTP requests, calls the service layer for business logic, and returns responses. Follows RESTful style.
*   **Service Layer (Services):** `YunLanCrm.Services` (Inferred, based on `YunLanCrm.IServices` interface definitions) and `YunLanCrm.Extensions`. Contains the implementation of core business logic.
*   **Interface Layer (Interfaces):** `YunLanCrm.IServices` directory. Defines service layer interfaces, enabling dependency inversion.
*   **Data Access Layer (Repository):** `YunLanCrm.Repository` (Inferred, based on `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Repository` and SqlSugar configuration). Interacts with the database, provides data persistence operations using SqlSugar ORM.
*   **Domain Models (Entities/Models):** `YunLanCrm.Model` (Inferred, based on `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Entity`). Defines data entities.
*   **Common/Extensions Layer (Common/Extensions):** `YunLanCrm.Common`, `YunLanCrm.Extensions`. Provides utility classes, middleware, service extensions, AOP, authentication/authorization support, etc.
*   **Workflow Layer (Workflow):** `YunLanCrm.Api/ElsaWorkflows`, `YunLanCrm.WorkFlow`. Integrates Elsa Workflow for business process automation.

## 3. Frontend Design

### 3.1 Technology Stack
*   **Core Framework:** Vue 3.x
*   **Build Tool:** Vite
*   **Programming Language:** TypeScript
*   **UI Framework:** Element Plus
*   **Routing Management:** Vue Router 4.x
*   **State Management:** Pinia
*   **HTTP Client:** Axios
*   **CSS Preprocessor:** Sass/SCSS
*   **Code Linting/Formatting:** ESLint, Prettier
*   **Other Libraries:** ECharts, WangEditor, LogicFlow, VxeTable, CountUp.js, SortableJS, Screenfull, etc.

### 3.2 Component Structure
*   **Directory Structure:**
    *   `src/components`: Global/reusable components.
    *   `src/views`: Page view components, organized by business modules.
    *   `src/layout`: Layout-related components.
*   **Reusable Components:** Encapsulated common components like icon selector (`iconSelector`), file upload (`scUpload`), rich text editor (`editor`), workflow designer (`scWorkflow`), table (`table`), etc.
*   **State Management (Pinia Stores):**
    *   `userInfo`: Stores user information and permissions.
    *   `themeConfig`: Theme configuration.
    *   `routesList`: Dynamic route list.
    *   `keepAliveNames`: Names of components to be cached.
    *   `tagsViewRoutes`: State for tab navigation (TagsView).

### 3.3 Routing Design
*   **Mode:** Hash or History mode (determined by `vite.config.ts` and Vue Router configuration).
*   **Route Definition:** `src/router/route.ts` defines static routes; `src/router/backEnd.ts` (inferred) handles dynamic routes returned from the backend.
*   **Navigation Logic:**
    *   Sidebar menu dynamically generated based on route configuration.
    *   Vue Router navigation guards (`beforeEach`) used for route permission control and login status validation.
    *   Supports tab navigation (TagsView).

### 3.4 API Calls
*   **Encapsulation:** API request functions are encapsulated by module in the `src/api` directory.
    *   `src/api/BaseApi.ts` (inferred) might contain Axios instance creation and base configuration (interceptors, etc.).
    *   `src/utils/request.ts` (inferred, or implemented within BaseApi) encapsulates Axios to uniformly handle request headers, token attachment, error handling, response interception, etc.
*   **Error Handling:** Axios interceptors uniformly handle HTTP status code errors and business logic error codes, providing user feedback (e.g., Element Plus Message or Notification).

### 3.5 User Interaction & UI Design
*   **UI Framework:** Element Plus provides the base component library and styles.
*   **Custom Styles:** The `src/theme` directory defines global styles, theme variables, overrides for Element Plus styles, and styles for specific components.
*   **Responsive Layout:** Likely achieved using CSS Media Queries or Element Plus's grid system to adapt to different screen sizes.
*   **Interaction Feedback:** NProgress used for page loading progress bars; Element Plus components like Loading, Message, Notification provide operational feedback.

## 4. Backend Design

### 4.1 Technology Stack
*   **Core Framework:** ASP.NET Core 6.0
*   **Enhanced Framework:** Furion
*   **Programming Language:** C#
*   **Web Server:** Kestrel (default), supports IIS deployment
*   **Database Interaction:** SqlSugar ORM
*   **Workflow:** Elsa Workflow
*   **Logging:** Serilog
*   **Authentication:** JWT
*   **Dependency Injection:** Autofac

### 4.2 Module Division (Inferred from directory structure)
*   **Controllers (`YunLanCrm.Api/Controllers`):** API entry points, handle HTTP requests, validate parameters, call Services. Controllers are divided by business domain (e.g., `CmTicketsController`, `UserController`).
*   **Services (`YunLanCrm.Services`, `YunLanCrm.Extensions`):** Implement interfaces defined in `YunLanCrm.IServices`, handle core business logic.
*   **Repositories (`YunLanCrm.Repository`):** Implement data access logic, operate the database via SqlSugar.
*   **Entities (`YunLanCrm.Model`):** Entity classes corresponding to database tables.
*   **Common (`YunLanCrm.Common`):** Utility classes, constants, helper classes (e.g., `StringHelper`, `UploadHelper`), custom authorization, middleware base classes, etc.
*   **Extensions (`YunLanCrm.Extensions`):** Service registration extension methods (e.g., `AddSqlsugarSetup`, `AddAuthentication_JWTSetup`), middleware, AOP implementation, AutoMapper configuration, etc.
*   **Workflows (`YunLanCrm.Api/ElsaWorkflows`, `YunLanCrm.WorkFlow`):** Elsa workflow definitions and activity implementations.

### 4.3 Data Model Design
*   **ORM:** SqlSugar used for object-relational mapping.
*   **Entity Definition:** C# classes corresponding to database tables defined in `YunLanCrm.Model` or `YunLanCrm.FrameWork/YunLanCrm.FrameWork.Entity` (inferred).
*   **Database Tables:** (Requires further analysis of entity classes or DB scripts) Expected to include tables like Users, Roles, Permissions, Menus, CmTickets (main ticket table), CmTicketDetails/CmTicketContents, CmTicketComments, CmTicketAttachments, WorkflowInstances, WorkflowExecutionLogRecords, LogEvent, etc.
*   **Fields:** (Requires further analysis of entity classes) Include primary keys, foreign keys, business fields, audit fields (creation time, creator, modification time, modifier, etc.).

### 4.4 API Design
*   **Specification:** RESTful API style.
*   **Routing:** Uses attribute routing (`[Route]`, `[HttpGet]`, `[HttpPost]`, etc.), potentially with a global prefix (e.g., `/api`) added via `GlobalRoutePrefixFilter`.
*   **Data Format:** Request and response bodies use JSON format. Serialization options (like camelCase naming, date format) configured via `AddNewtonsoftJson`.
*   **Endpoint Documentation:** (Requires checking Controller code and XML comments `YunLanCrm.xml`) Action methods in Controllers correspond to specific API endpoints. XML comments are used for generating Swagger documentation.
*   **Authentication Mechanism:**
    *   APIs requiring authentication are protected using the `[Authorize]` attribute.
    *   JWT Bearer Token authentication: Frontend sends the token in the `Authorization` header.
    *   Backend configures JWT validation logic (Issuer, Audience, Secret Key, etc.) via `AddAuthentication_JWTSetup`.
    *   `/api/Token` (inferred) or a similar endpoint used for user login and token retrieval.
*   **Unified Response Format:** Furion's `AddInjectWithUnifyResult<RESTfulResultProvider>` implements a unified API response structure, including success/failure status codes, messages, and data.

### 4.5 Exception Handling and Logging
*   **Global Exception Handling:** `GlobalExceptionsFilter` catches unhandled exceptions, logs them, and returns a standardized error response.
*   **Logging:**
    *   Serilog used for logging.
    *   Multiple sinks configured: Console, File (split by day, by level), SQL Server (`LogEvent` table).
    *   `LogContext` used to add contextual information.
    *   Log levels, filtering rules, and output templates configured in `Program.cs`.
*   **Error Codes:** (Requires checking code implementation) Business error codes might be defined to distinguish different business exception scenarios.

## 5. Database Design (Inferred)

### 5.1 Database Architecture
*   **Type:** Microsoft SQL Server.
*   **Interaction:** Data operations performed via SqlSugar ORM.
*   **Connection String:** Configured in `appsettings.json` or other configuration files (`Serilog:ConnectionString`, and SqlSugar's configuration).

### 5.2 Table Structure (Inferred, partial examples)
*   `Users`: User information (ID, Username, PasswordHash, Email, Status, etc.)
*   `Roles`: Role information (ID, RoleName)
*   `UserRoles`: User-role relationship table
*   `Permissions`: Permission definition table (ID, PermissionIdentifier, Name)
*   `RolePermissions`: Role-permission relationship table
*   `Menus`: Menu information table
*   `CmTickets`: Main ticket table (ID, Title, Description, Status, Priority, Creator, CreationTime, Assignee, etc.)
*   `CmTicketDetails` / `CmTicketContents`: Ticket content/processing record table
*   `CmTicketComments`: Ticket comment table
*   `CmTicketAttachments`: Ticket attachment table
*   `WorkflowInstances`: Workflow instance table (Elsa)
*   `WorkflowExecutionLogRecords`: Workflow execution log (Elsa)
*   `LogEvent`: Serilog log record table

### 5.3 Data Storage and Index Optimization
*   (Requires checking DB scripts or SqlSugar configuration) Indexes might be created on frequently queried fields (like ID, status, creation time, user ID) to improve query performance.

### 5.4 Transaction Management, Data Consistency Strategy
*   (Requires checking Service layer code) SqlSugar supports transaction operations. Critical business operations (e.g., involving updates to multiple tables) should be wrapped in transactions to ensure atomicity.
*   For distributed transactions or complex consistency scenarios, introducing the Saga pattern or a distributed transaction coordinator might be necessary (not explicitly evident in the code).

## 6. Security Design

### 6.1 Frontend Security
*   **XSS (Cross-Site Scripting):**
    *   Vue escapes HTML in template interpolations by default.
    *   Content rendered using `v-html` or from rich text editors requires strict XSS filtering.
*   **CSRF (Cross-Site Request Forgery):**
    *   JWT itself offers some protection against CSRF (if tokens are stored in LocalStorage/SessionStorage). If tokens are stored in Cookies, the backend needs an Anti-CSRF Token mechanism.
*   **CORS (Cross-Origin Resource Sharing):** Backend configured via `AddCorsSetup` to allow access from specific frontend origins.
*   **Dependency Security:** Regularly update dependencies to avoid using versions with known vulnerabilities.

### 6.2 Backend Security
*   **Authentication:**
    *   Token-based authentication using JWT.
    *   Password Storage: Should use strong hashing algorithms (e.g., bcrypt, Argon2) with salt.
*   **Authorization:**
    *   Role-Based Access Control (RBAC).
    *   API access controlled using the `[Authorize]` attribute combined with roles or policies.
    *   Relevant implementations found in `YunLanCrm.Common/Authorizations` and `YunLanCrm.Extensions/Authorizations`.
*   **Data Encryption:**
    *   Transport Layer: HTTPS should be enforced ( `UseHttpsRedirection` is commented out in code, recommended for production).
    *   Sensitive Data: Sensitive information in the database (like passwords, keys) should be encrypted or hashed.
*   **Input Validation:** Strict validation of all input from the frontend (non-null, format, length, type, etc.) to prevent SQL injection, command injection, etc. ASP.NET Core's model binding and validation mechanisms can be used.
*   **Dependency Injection Security:** Avoid exposing excessive internal details in service implementations.
*   **Configuration Security:** Sensitive configuration (DB passwords, JWT secrets) should not be hardcoded; use environment variables, Secrets Manager, or a configuration center (like Apollo, related config exists but might not be active).

### 6.3 API Security
*   **Authentication:** JWT Bearer Token.
*   **Authorization:** Role-based API access control.
*   **Rate Limiting:** IP rate limiting configured via `AddIpPolicyRateLimitSetup` to prevent brute-force attacks and DoS.
*   **Parameter Validation:** Backend performs strict validation on API parameters.
*   **Information Leakage Prevention:** Global exception handling prevents exposing raw stack traces to clients.

## 7. Deployment & Maintenance

### 7.1 Frontend Build and Deployment
*   **Build:** Use `npm run build` (or `vite build`) to bundle the Vue project into static files (HTML, CSS, JS).
*   **Deployment:**
    *   Deploy the built static files to a web server (like Nginx, IIS) or object storage (like Azure Blob Storage).
    *   The `public/web.config` file suggests potential IIS deployment.
    *   CDN: Optional, for accelerating static asset delivery.
    *   Mode: Client-Side Rendering (CSR) is the default mode.

### 7.2 Backend Deployment
*   **Server Architecture:** Can be deployed on Windows Server (IIS) or Linux servers.
*   **Containerization:** The project provides a `Dockerfile`, supporting deployment using Docker containers.
    *   Scripts like `DockerBuild.bat`, `YunLanCrm.Publish.Docker.sh` assist with Docker build and publish processes.
*   **Load Balancing:** (Not directly evident in code) Production environments typically require a load balancer (e.g., Nginx, HAProxy, Azure Load Balancer) to distribute requests across multiple backend instances.
*   **Orchestration:** (Not directly evident in code) For large-scale deployments, container orchestration tools like Kubernetes can be used to manage Docker containers.

### 7.3 CI/CD Pipeline
*   (Requires checking the project repository configuration) Tools like GitHub Actions, Jenkins, GitLab CI, or Azure DevOps might be used for automated build, test, and deployment pipelines.
*   The `YunLanCrm.Publish.Docker.Jenkins.sh` file suggests potential use of Jenkins for Docker-related CI/CD.
*   `codecov.yml` indicates integration with Codecov for code coverage checks.

## 8. Testing & Optimization

### 8.1 Frontend Testing
*   (Test files not explicitly found in the codebase)
*   **Unit Testing:** Vitest or Jest + Vue Testing Library can be used for unit testing components, utility functions, and Pinia stores.
*   **End-to-End Testing (E2E):** Cypress or Playwright can simulate user interactions to test critical business flows.

### 8.2 Backend Testing
*   (Test projects not explicitly found in the codebase)
*   **Unit Testing:** xUnit or NUnit can be used for unit testing the Service layer and utility classes, potentially requiring mocking dependencies (like Repositories).
*   **Integration Testing:** `Microsoft.AspNetCore.Mvc.Testing` can be used for integration testing API Controllers, covering request handling, service calls, and database interactions.
*   **API Testing:** Tools like Postman, Newman, or other API testing tools used for functional and regression testing of the deployed APIs.

### 8.3 Performance Optimization
*   **Frontend:**
    *   **Code Splitting/Lazy Loading:** Vite and Vue Router support route-based lazy loading by default, loading page components on demand.
    *   **Static Asset Optimization:** `vite-plugin-compression` used for asset compression (Gzip/Brotli). `vite-plugin-cdn-import` might be used for CDN resources.
    *   **Image Optimization:** Compress image sizes.
    *   **Virtual Scrolling/List Optimization:** Use virtual scrolling techniques for long lists (VxeTable might support this).
    *   **Reduce Reflows/Repaints:** Optimize DOM manipulations.
    *   **Caching:** Utilize browser caching effectively. Pinia state persistence.
*   **Backend:**
    *   **Database Optimization:**
        *   Optimize SQL queries (SqlSugar performance).
        *   Add appropriate database indexes.
    *   **Caching:**
        *   Use memory caching (`IMemoryCache`) for hot data or configuration.
        *   Consider distributed caching (like Redis, included but commented out) for scalability.
    *   **Asynchronous Processing:** Use `async/await` for I/O-bound operations.
    *   **Response Compression:** Enabled Brotli and Gzip compression (`AddResponseCompression`).
    *   **Code Optimization:** Optimize algorithms and data structures.
    *   **Performance Profiling:** Use MiniProfiler (`AddMiniProfilerSetup`) to identify performance bottlenecks.
    *   **Horizontal Scaling:** Improve throughput by deploying multiple instances behind a load balancer.
*   **API:**
    *   Reduce unnecessary API calls.
    *   Optimize API response data size.
