# CaseX 管理系统设计文档

## 1. 项目概述

**项目名称**: BPTS CaseX 管理系统  
**版本**: 1.0.0  
**描述**: 基于Vue3的工单/案例管理系统，具备工作流功能  
**目标用户**: 需要工单管理和工作流自动化的内部团队  
**核心功能**:
- 工单创建、跟踪和管理
- 工作流自动化
- 基于角色的访问控制
- 多主题支持
- 国际化(中英文)
- 响应式布局

## 2. 系统架构

### 2.1 技术栈
- **前端**: Vue 3 + TypeScript + Vite
- **UI框架**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **国际化**: Vue I18n
- **构建工具**: Vite

### 2.2 架构图

![系统架构图](img/system-architecture.svg)

## 3. 模块设计

### 3.1 核心模块
1. **认证模块**
   - 基于Token的认证
   - 自动登出
   - 强制密码修改

2. **工单管理**
   - 工单增删改查
   - 评论和描述
   - 状态跟踪(如挂起等)
   - 批量操作

3. **工作流引擎**
   - 基于图表的工作流设计(@logicflow)
   - 审批流程
   - 执行跟踪

4. **UI/主题系统**
   - 多种布局选项
   - 色彩方案管理
   - 响应式设计

5. **系统管理**
   - 用户管理
   - 角色权限
   - 系统配置

### 3.2 主要用例图

```mermaid
graph TD
    subgraph SystemBoundary["CaseX 管理系统"]
        direction LR
        UC1(创建/管理工单)
        UC2(设计/管理工作流)
        UC3(管理用户/角色)
        UC4(查看仪表盘/报表)
        UC5(处理审批任务)
    end

    Actor1[普通用户] --> UC1
    Actor1 --> UC5
    Actor1 --> UC4

    Actor2[管理员] --> UC1
    Actor2 --> UC2
    Actor2 --> UC3
    Actor2 --> UC4
    Actor2 --> UC5

    style SystemBoundary fill:#eee,stroke:#333,stroke-width:1px,stroke-dasharray: 5 5
```

### 3.3 模块结构图

![模块结构图](img/module-structure.svg)

## 4. 关键技术实现

### 4.1 状态管理
- Pinia存储:
  - 主题配置
  - 用户信息
  - 路由管理
  - 标签页状态
  - 缓存管理

### 4.2 API通信
- 基础CRUD操作标准化(BaseApi)
- 领域特定API继承BaseApi
- 统一的错误处理
- 支持参数和请求体两种负载

### 4.3 安全特性
- JWT令牌认证
- 自动令牌刷新
- 存储加密
- 会话管理
- 版本验证

## 5. 数据库设计

### 5.1 核心表结构

#### cmTickets (工单表)
| 字段名 | 类型 | 是否为空 | 描述 |
|--------|------|----------|------|
| Id | bigint | 否 | 主键 |
| Ticket_Number | varchar(100) | 否 | 工单编号 |
| Ticket_Status | varchar(100) | 否 | 工单状态 |
| Ticket_Subject | varchar(50) | 否 | 工单标题 |
| Ticket_Priority | varchar(50) | 否 | 优先级 |
| Ticket_Category | varchar(100) | 否 | 分类 |
| Ticket_First_Name | varchar(100) | 否 | 客户名 |
| Ticket_Last_Name | varchar(100) | 否 | 客户姓 |
| Ticket_Customer | varchar(100) | 否 | 客户ID |
| Ticket_Customer_Email | varchar(100) | 否 | 客户邮箱 |
| Ticket_Customer_Phone | varchar(100) | 否 | 客户电话 |
| CreatedAt | datetime | 否 | 创建时间 |
| ModifiedAt | datetime | 否 | 修改时间 |
| Ticket_Open_Date | datetime | 是 | 打开时间 |
| Ticket_Close_Date | datetime | 是 | 关闭时间 |

#### User (用户表)
| 字段名 | 类型 | 是否为空 | 描述 |
|--------|------|----------|------|
| Id | int | 否 | 主键 |
| UserName | varchar(200) | 是 | 登录账号 |
| LoginPWD | varchar(200) | 是 | 密码 |
| RealName | varchar(200) | 是 | 真实姓名 |
| Status | int | 否 | 状态(0正常/1禁用) |
| DepartmentId | int | 是 | 部门ID |
| CreateTime | datetime | 否 | 创建时间 |
| UpdateTime | datetime | 否 | 更新时间 |
| Mobile | varchar(50) | 是 | 手机号 |
| Email | varchar(100) | 是 | 邮箱 |
| Sex | int | 是 | 性别 |
| Age | int | 是 | 年龄 |
| Address | varchar(200) | 是 | 地址 |

#### Workflow_Design (工作流设计表)
| 字段名 | 类型 | 是否为空 | 描述 |
|--------|------|----------|------|
| DesignId | uniqueidentifier | 否 | 主键 |
| WorkflowName | varchar(255) | 否 | 工作流名称 |
| WorkflowCode | varchar(50) | 是 | 工作流编码 |
| Status | int | 否 | 状态(0正常/1禁用) |
| Data | ntext | 是 | 工作流JSON数据 |
| Description | varchar(1000) | 是 | 描述 |
| CreatedAt | datetime | 否 | 创建时间 |
| UpdatedAt | datetime | 是 | 更新时间 |

### 5.2 表关系
1. cmTickets.Ticket_Open_By 关联 User.Id (工单创建人)
2. cmTickets.Ticket_Close_By 关联 User.Id (工单关闭人)
3. Workflow_Design 通过WorkflowId关联工作流实例表

## 6. API接口设计规范

### 6.1 接口基础信息
- 认证方式: JWT Bearer Token
- 基础路径: /Api/[controller]
- 响应格式: JSON
- 错误码:
  - 200: 请求成功
  - 400: 请求参数错误
  - 401: 未授权
  - 403: 禁止访问
  - 500: 服务器内部错误

### 6.2 工单管理API

#### 创建工单
**接口路径**: /Api/CmTickets/Add  
**HTTP方法**: POST  
**请求头**: Authorization: Bearer {token}  
**请求体**:
```json
{
  "Ticket_Subject": "打印机无法使用",
  "Ticket_Priority": "高",
  "Ticket_Category": "IT问题",
  "Ticket_First_Name": "张",
  "Ticket_Last_Name": "三",
  "Ticket_Customer_Email": "<EMAIL>"
}
```
**成功响应**:
```json
{
  "code": 200,
  "data": 12345,
  "message": "工单创建成功"
}
```
**参数说明**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Ticket_Subject | string | 是 | 工单标题 |
| Ticket_Priority | string | 是 | 优先级(高/中/低) |
| Ticket_Category | string | 是 | 问题分类 |

**使用场景**: 用户提交新的工单请求

#### 查询工单详情
**接口路径**: /Api/CmTickets/Get/{id}  
**HTTP方法**: GET  
**请求示例**: /Api/CmTickets/Get/12345  
**成功响应**:
```json
{
  "code": 200,
  "data": {
    "Id": 12345,
    "Ticket_Number": "TKT-20230001",
    "Ticket_Status": "处理中",
    "Ticket_Subject": "打印机无法使用",
    "CreatedAt": "2023-01-01T10:00:00"
  }
}
```

### 6.3 用户管理API

#### 创建用户
**接口路径**: /Api/User/Add  
**HTTP方法**: POST  
**请求体**:
```json
{
  "UserName": "zhangsan",
  "RealName": "张三",
  "Password": "123456",
  "Email": "<EMAIL>",
  "DepartmentId": 1
}
```

#### 修改密码
**接口路径**: /Api/User/ChangePassword  
**HTTP方法**: POST  
**请求体**:
```json
{
  "OldPassword": "123456",
  "NewPassword": "654321"
}
```

### 6.4 工作流API

#### 创建工作流设计
**接口路径**: /Api/WorkflowDesign/Add  
**HTTP方法**: POST  
**请求体**:
```json
{
  "WorkflowName": "请假审批流程",
  "Description": "员工请假审批工作流",
  "Data": "{...工作流定义JSON...}"
}
```

### 6.5 接口调用示例
```javascript
// 使用axios调用创建工单接口
const createTicket = async () => {
  try {
    const response = await axios.post('/Api/CmTickets/Add', {
      Ticket_Subject: '打印机问题',
      Ticket_Priority: '高'
    }, {
      headers: {
        'Authorization': 'Bearer ' + token
      }
    });
    console.log('工单创建成功:', response.data);
  } catch (error) {
    console.error('创建工单失败:', error.response.data);
  }
}
```

## 7. 部署方案
- 基于Vite的构建流程
- 静态资源优化
- 环境特定配置
- 版本化发布与自动更新检查
- 

## 8. 未来改进
- 增强报表功能
- 移动端优化
- 更多工作流触发器
- 高级搜索功能
