<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 500">
  <!-- 背景 -->
  <rect width="800" height="500" fill="#f5f7fa" rx="10" ry="10"/>
  
  <!-- 标题 -->
  <text x="400" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#2c3e50">CaseX 系统架构</text>

  <!-- 浏览器层 -->
  <rect x="100" y="70" width="600" height="60" rx="6" ry="6" fill="#3498db" stroke="#2980b9" stroke-width="2"/>
  <text x="400" y="105" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="white">浏览器</text>
  
  <!-- 连接线 -->
  <line x1="300" y1="130" x2="300" y2="170" stroke="#7f8c8d" stroke-width="3" stroke-dasharray="5,5"/>
  <line x1="500" y1="130" x2="500" y2="170" stroke="#7f8c8d" stroke-width="3" stroke-dasharray="5,5"/>
  
  <!-- 前端与后端容器 -->
  <rect x="100" y="170" width="300" height="300" rx="6" ry="6" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <text x="250" y="200" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#2c3e50">前端 (Vue 3)</text>
  
  <rect x="400" y="170" width="300" height="300" rx="6" ry="6" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <text x="550" y="200" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#2c3e50">后端</text>
  
  <!-- 前端模块 -->
  <rect x="130" y="220" width="240" height="50" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
  <text x="250" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">路由 (Vue Router)</text>
  
  <line x1="250" y1="270" x2="250" y2="290" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <rect x="130" y="290" width="240" height="50" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
  <text x="250" y="320" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">组件 (Element Plus)</text>
  
  <line x1="250" y1="340" x2="250" y2="360" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <rect x="130" y="360" width="240" height="50" rx="5" ry="5" fill="#9b59b6" stroke="#8e44ad" stroke-width="2"/>
  <text x="250" y="390" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">状态管理 (Pinia)</text>
  
  <!-- 后端模块 -->
  <rect x="430" y="220" width="240" height="50" rx="5" ry="5" fill="#27ae60" stroke="#16a085" stroke-width="2"/>
  <text x="550" y="250" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">API 层</text>
  
  <line x1="550" y1="270" x2="550" y2="290" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <rect x="430" y="290" width="240" height="50" rx="5" ry="5" fill="#27ae60" stroke="#16a085" stroke-width="2"/>
  <text x="550" y="320" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">业务逻辑</text>
  
  <line x1="550" y1="340" x2="550" y2="360" stroke="#7f8c8d" stroke-width="2" stroke-dasharray="5,5"/>
  
  <rect x="430" y="360" width="240" height="50" rx="5" ry="5" fill="#27ae60" stroke="#16a085" stroke-width="2"/>
  <text x="550" y="390" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">数据库</text>
  
  <!-- 通信箭头 -->
  <path d="M370 245 L430 245" stroke="#e74c3c" stroke-width="2" fill="none"/>
  <polygon points="430,245 420,240 420,250" fill="#e74c3c"/>
  
  <path d="M430 310 L370 310" stroke="#e74c3c" stroke-width="2" fill="none"/>
  <polygon points="370,310 380,305 380,315" fill="#e74c3c"/>
</svg>
