/**
 * 缓存管理工具
 * 用于处理浏览器缓存清理和版本控制
 */

/**
 * 清除浏览器缓存
 */
export const clearBrowserCache = () => {
  try {
    // 清除 Service Worker 缓存
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        registrations.forEach(registration => {
          registration.unregister();
        });
      });
    }

    // 清除 Cache API 缓存
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        cacheNames.forEach(cacheName => {
          caches.delete(cacheName);
        });
      });
    }

    console.log('Browser cache cleared successfully');
  } catch (error) {
    console.error('Error clearing browser cache:', error);
  }
};

/**
 * 强制刷新页面并清除缓存
 * @param version 新版本号
 */
export const forceReloadWithCacheBust = (version?: string) => {
  try {
    // 清除浏览器缓存
    clearBrowserCache();

    // 构建带有缓存破坏参数的URL
    const url = new URL(window.location.href);
    url.searchParams.set('t', Date.now().toString());
    
    if (version) {
      url.searchParams.set('v', version);
    }

    // 强制刷新页面
    window.location.href = url.toString();
  } catch (error) {
    console.error('Error during force reload:', error);
    // 降级方案：简单刷新
    window.location.reload();
  }
};

/**
 * 检查是否需要清除缓存
 * @param currentVersion 当前版本
 * @param remoteVersion 远程版本
 * @returns 是否需要清除缓存
 */
export const shouldClearCache = (currentVersion: string | null, remoteVersion: string): boolean => {
  if (!currentVersion) {
    return true;
  }
  
  return currentVersion !== remoteVersion;
};

/**
 * 添加缓存破坏参数到URL
 * @param url 原始URL
 * @param version 版本号
 * @returns 带有缓存破坏参数的URL
 */
export const addCacheBustToUrl = (url: string, version?: string): string => {
  try {
    const urlObj = new URL(url, window.location.origin);
    urlObj.searchParams.set('t', Date.now().toString());
    
    if (version) {
      urlObj.searchParams.set('v', version);
    }
    
    return urlObj.toString();
  } catch (error) {
    console.error('Error adding cache bust to URL:', error);
    // 降级方案：简单添加时间戳
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}t=${Date.now()}${version ? `&v=${version}` : ''}`;
  }
};

/**
 * 获取无缓存的fetch配置
 */
export const getNoCacheHeaders = () => ({
  cache: 'no-cache' as RequestCache,
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
});

/**
 * 清除特定的localStorage项目，但保留重要数据
 * @param keysToKeep 需要保留的key列表
 */
export const clearLocalStorageExcept = (keysToKeep: string[] = []) => {
  try {
    const itemsToRemove: string[] = [];
    
    // 收集需要删除的项目
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && !keysToKeep.includes(key)) {
        itemsToRemove.push(key);
      }
    }
    
    // 删除项目
    itemsToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`Cleared ${itemsToRemove.length} localStorage items, kept ${keysToKeep.length} items`);
  } catch (error) {
    console.error('Error clearing localStorage:', error);
  }
};

/**
 * 清除sessionStorage
 */
export const clearSessionStorage = () => {
  try {
    sessionStorage.clear();
    console.log('SessionStorage cleared successfully');
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
  }
};
